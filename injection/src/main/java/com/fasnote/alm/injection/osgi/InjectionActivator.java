package com.fasnote.alm.injection.osgi;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceRegistration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.injection.impl.DependencyInjector;
import com.fasnote.alm.plugin.manage.injection.module.BundleContextProvider;

/**
 * OSGi Bundle激活器
 * 负责启动和停止依赖注入框架
 */
public class InjectionActivator implements BundleActivator {
    
    private static final Logger logger = LoggerFactory.getLogger(InjectionActivator.class);
    private static BundleContext bundleContext;
    
    private static IDependencyInjector dependencyInjector;
    private ServiceRegistration<IDependencyInjector> serviceRegistration;

	/**
	 * BundleContext 服务提供者
	 */
	private class BundleContextProvider implements IServiceProvider<BundleContext> {

		@Override
		public BundleContext provide(IInjectionContext context) throws Exception {
			if (bundleContext == null) {
				throw new RuntimeException("BundleContext不可用");
			}
			logger.debug("提供BundleContext实例: {}", bundleContext.getBundle().getSymbolicName());
			return bundleContext;
		}
	}
    @Override
    public void start(BundleContext context) throws Exception {
        logger.info("启动ALM依赖注入框架");
        
        try {
            dependencyInjector = new DependencyInjector(context);
            startBundleTracking();

            logger.info("ALM依赖注入框架启动成功");
            
        } catch (Exception e) {
            logger.error("启动ALM依赖注入框架失败", e);
            throw e;
        }
    }
    
    @Override
    public void stop(BundleContext context) throws Exception {
        logger.info("停止ALM依赖注入框架");
        
        try {
            // 1. 停止Bundle跟踪
            stopBundleTracking();

            // 2. 注销OSGi服务
            if (serviceRegistration != null) {
                serviceRegistration.unregister();
                serviceRegistration = null;
            }

            // 3. 清理依赖注入器
            if (dependencyInjector != null) {
                dependencyInjector.clear();
                dependencyInjector = null;
            }
            
            logger.info("ALM依赖注入框架停止成功");
            
        } catch (Exception e) {
            logger.error("停止ALM依赖注入框架失败", e);
            throw e;
        }
    }
    
    /**
     * 获取依赖注入器实例
     */
    public static IDependencyInjector getDependencyInjector() {
        return dependencyInjector;
    }
    


    /**
     * 启动Bundle跟踪机制
     */
    private void startBundleTracking() {
        logger.debug("启动Bundle跟踪机制");

        try {
            if (dependencyInjector instanceof com.fasnote.alm.injection.impl.DependencyInjector) {
                com.fasnote.alm.injection.impl.DependencyInjector diImpl =
                    (com.fasnote.alm.injection.impl.DependencyInjector) dependencyInjector;
                diImpl.startPackageScanProviderTracking();
                logger.info("包扫描提供者跟踪机制启动成功");
            } else {
                logger.warn("依赖注入器不支持包扫描提供者跟踪");
            }
        } catch (Exception e) {
            logger.error("启动Bundle跟踪机制失败", e);
        }
    }

    /**
     * 停止Bundle跟踪机制
     */
    private void stopBundleTracking() {
        logger.debug("停止Bundle跟踪机制");

        try {
            if (dependencyInjector instanceof com.fasnote.alm.injection.impl.DependencyInjector) {
                com.fasnote.alm.injection.impl.DependencyInjector diImpl =
                    (com.fasnote.alm.injection.impl.DependencyInjector) dependencyInjector;
                diImpl.stopPackageScanProviderTracking();
                logger.info("包扫描提供者跟踪机制已停止");
            }
        } catch (Exception e) {
            logger.error("停止Bundle跟踪机制失败", e);
        }
    }
}